/**
 * Van As News Importer Admin JavaScript
 */
jQuery(document).ready(function($) {
    // Elements
    const importForm = $('#van-as-import-form');
    const importButton = $('#van-as-import-button');
    const progressContainer = $('.van-as-progress-container');
    const progressBar = $('.van-as-progress-bar');
    const importStatus = $('.van-as-import-status');

    // Variables
    let importRunning = false;
    let progressInterval = null;
    let currentProgress = 0;

    // Handle import form submission
    importForm.on('submit', function(e) {
        e.preventDefault();

        // Don't allow multiple imports at once
        if (importRunning) {
            return false;
        }

        // Start the import
        startImport();

        return false;
    });

    // Start the import process
    function startImport() {
        importRunning = true;

        // Update UI
        importButton.prop('disabled', true);
        progressContainer.show();
        importStatus.text(vanAsAdmin.importing_text).show();

        // Reset progress
        currentProgress = 0;
        updateProgressBar(0);

        // Get the selected_only checkbox value
        const selectedOnly = $('#van-as-import-form input[name="selected_only"]').is(':checked') ? 1 : 0;

        // Make the AJAX request to start the import
        $.ajax({
            url: vanAsAdmin.ajax_url,
            type: 'POST',
            data: {
                action: 'van_as_start_import',
                nonce: vanAsAdmin.nonce,
                selected_only: selectedOnly
            },
            success: function(response) {
                if (response.success) {
                    // Start checking progress
                    progressInterval = setInterval(checkImportProgress, 2000);
                } else {
                    importError(response.data.message || vanAsAdmin.error_text);
                }
            },
            error: function() {
                importError(vanAsAdmin.error_text);
            }
        });
    }

    // Check the import progress
    function checkImportProgress() {
        $.ajax({
            url: vanAsAdmin.ajax_url,
            type: 'POST',
            data: {
                action: 'van_as_import_progress',
                nonce: vanAsAdmin.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Update progress
                    updateProgress(response.data);

                    // Check if import is complete
                    if (response.data.complete) {
                        importComplete(response.data);
                    }
                } else {
                    importError(response.data.message || vanAsAdmin.error_text);
                }
            },
            error: function() {
                importError(vanAsAdmin.error_text);
            }
        });
    }

    // Update the progress display
    function updateProgress(data) {
        // Calculate percentage
        const percentage = data.percentage || 0;

        // Update progress bar
        updateProgressBar(percentage);

        // Update status message
        if (data.status_message) {
            importStatus.text(data.status_message);
        }
    }

    // Update the progress bar
    function updateProgressBar(percentage) {
        progressBar.css('width', percentage + '%');
        progressBar.text(percentage + '%');
    }

    // Handle import completion
    function importComplete(data) {
        // Clear the interval
        clearInterval(progressInterval);

        // Update UI
        updateProgressBar(100);
        importStatus.text(data.status_message || vanAsAdmin.complete_text);

        // Re-enable the import button after a delay
        setTimeout(function() {
            importButton.prop('disabled', false);
            importRunning = false;
        }, 2000);

        // Reload the page after a delay to show the updated import status
        setTimeout(function() {
            window.location.reload();
        }, 3000);
    }

    // Handle import error
    function importError(message) {
        // Clear the interval
        clearInterval(progressInterval);

        // Update UI
        importStatus.text(message).css('color', 'red');

        // Re-enable the import button
        importButton.prop('disabled', false);
        importRunning = false;
    }
});
