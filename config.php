<?php
/**
 * Van As API Importer - Configuratie <PERSON>and
 *
 * Plaats dit bestand in /wp-content/plugins/van-as-news-importer/config.php
 * en pas de instellingen aan naar wens.
 */

// Voorkom directe toegang
if (!defined('ABSPATH')) {
    exit;
}

class VanAsConfig {

    // API Instellingen
    const API_BASE_URL = 'https://vanas.contador.be/api';
    const API_USERNAME = 'admin';
    const API_PASSWORD = '<EMAIL>';

    // Import Instellingen
    const IMPORT_FREQUENCY = 'daily'; // hourly, twicedaily, daily, weekly
    const POST_STATUS = 'publish'; // draft, publish, private
    const POST_TYPE = 'post'; // post, page, of custom post type
    const DEFAULT_AUTHOR_ID = 1;
    const DEFAULT_CATEGORY_ID = 1;
    const IMPORT_SELECTED_ONLY = true; // Alleen geselecteerde artikelen importeren

    // Content Filtering
    const MIN_CONTENT_LENGTH = 100; // Minimum aantal karakters voor import
    const MAX_CONTENT_LENGTH = 50000; // Maximum aantal karakters
    const ALLOWED_HTML_TAGS = array(
        'p', 'br', 'strong', 'em', 'ul', 'ol', 'li', 'a', 'img', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'
    );

    // Image Instellingen
    const DOWNLOAD_FEATURED_IMAGES = true;
    const MAX_IMAGE_SIZE = 5242880; // 5MB in bytes
    const ALLOWED_IMAGE_TYPES = array('jpg', 'jpeg', 'png', 'gif', 'webp');

    // Logging Instellingen
    const ENABLE_LOGGING = true;
    const LOG_FILE_PATH = WP_CONTENT_DIR . '/van-as-api-logs.txt';
    const MAX_LOG_LINES = 1000;
    const LOG_LEVEL = 'INFO'; // DEBUG, INFO, WARNING, ERROR

    // Email Notificaties
    const SEND_ERROR_EMAILS = true;
    const ERROR_EMAIL_RECIPIENT = '<EMAIL>';
    const SUCCESS_EMAIL_SUMMARY = false; // true voor dagelijkse samenvatting

    // Performance Instellingen
    const API_TIMEOUT = 30; // seconden
    const BATCH_SIZE = 10; // aantal artikelen per batch
    const MEMORY_LIMIT = '256M';
    const MAX_EXECUTION_TIME = 300; // 5 minuten

    // WordPress Instellingen
    const CREATE_CATEGORIES = true; // Automatisch categorieën aanmaken
    const CREATE_TAGS = true; // Automatisch tags aanmaken
    const PRESERVE_FORMATTING = true; // HTML formatting behouden

    // Advanced Instellingen
    const USE_EXCERPT_AS_META_DESCRIPTION = true;
    const SET_FEATURED_IMAGE_ALT_TEXT = true;
    const GENERATE_SLUG_FROM_TITLE = true;

    /**
     * Haal configuratie waarde op
     */
    public static function get($key, $default = null) {
        if (defined('self::' . $key)) {
            return constant('self::' . $key);
        }
        return $default;
    }

    /**
     * Valideer configuratie
     */
    public static function validate() {
        $errors = array();

        // Valideer directories
        if (!is_writable(dirname(self::LOG_FILE_PATH))) {
            $errors[] = 'Log directory is niet schrijfbaar: ' . dirname(self::LOG_FILE_PATH);
        }

        // Valideer email adressen
        if (self::SEND_ERROR_EMAILS && !is_email(self::ERROR_EMAIL_RECIPIENT)) {
            $errors[] = 'Ongeldig email adres voor error notificaties';
        }

        return $errors;
    }

    /**
     * API Request types
     */
    const API_REQUEST_TYPES = array(
        'FULL',    // Alle artikelen
        'RANGE',   // Datum range (from/until in ddMMyyyy format)
        'LIMIT'    // Beperkt aantal artikelen
    );

    /**
     * Supported languages
     */
    const SUPPORTED_LANGUAGES = array(
        'nl' => 'Nederlands',
        'fr' => 'Frans'
    );

    const DEFAULT_LANGUAGE = 'nl';

    /**
     * Content filters die toegepast worden voor import
     */
    public static function filter_content($content) {
        // Lengte check
        if (strlen($content) < self::MIN_CONTENT_LENGTH) {
            return false;
        }

        if (strlen($content) > self::MAX_CONTENT_LENGTH) {
            $content = substr($content, 0, self::MAX_CONTENT_LENGTH) . '...';
        }

        // HTML cleaning
        if (self::PRESERVE_FORMATTING) {
            $allowed_tags = '<' . implode('><', self::ALLOWED_HTML_TAGS) . '>';
            $content = strip_tags($content, $allowed_tags);
        }

        // Basis content cleaning
        $content = wp_kses_post($content);
        $content = trim($content);

        return $content;
    }

    /**
     * API Request builder voor verschillende import types
     */
    public static function build_api_request($type = 'FULL', $options = array()) {
        $request = array(
            'type' => $type,
            'language' => self::DEFAULT_LANGUAGE
        );

        // Voeg selected parameter toe om alleen geselecteerde artikelen op te halen
        $request['selected'] = self::IMPORT_SELECTED_ONLY;

        switch ($type) {
            case 'RANGE':
                if (isset($options['from'])) {
                    $request['from'] = $options['from']; // ddMMyyyy format
                }
                if (isset($options['until'])) {
                    $request['until'] = $options['until']; // ddMMyyyy format
                }
                break;

            case 'LIMIT':
                if (isset($options['limit'])) {
                    $request['limit'] = (int) $options['limit'];
                }
                break;

            case 'FULL':
            default:
                // Geen extra parameters nodig
                break;
        }

        // Override defaults met custom options
        return array_merge($request, $options);
    }

    /**
     * Krijg email templates
     */
    public static function get_email_templates() {
        return array(
            'error' => array(
                'subject' => '[Van As] API Import Error',
                'body' => 'Er is een fout opgetreden tijdens het importeren van artikelen:\n\n{error_message}\n\nTijd: {timestamp}\n\nControleer de logs voor meer details.'
            ),
            'success_summary' => array(
                'subject' => '[Van As] Dagelijkse Import Samenvatting',
                'body' => 'Import samenvatting voor {date}:\n\n- Geïmporteerd: {imported_count} artikelen\n- Overgeslagen: {skipped_count} artikelen\n- Fouten: {error_count}\n\nLaatste update: {timestamp}'
            )
        );
    }

    /**
     * Category mapping - pas aan indien nodig
     */
    public static function get_category_mapping() {
        return array(
            'nieuws' => 'Nieuws',
            'updates' => 'Updates',
            'productinfo' => 'Product Informatie',
            'blog' => 'Blog',
            // Voeg meer mappings toe indien nodig
        );
    }

    /**
     * Debug mode check
     */
    public static function is_debug_mode() {
        return false; // Debug mode uitgeschakeld voor productie
    }
}

// Configuratie validatie bij laden
if (is_admin()) {
    $config_errors = VanAsConfig::validate();
    if (!empty($config_errors)) {
        add_action('admin_notices', function() use ($config_errors) {
            echo '<div class="notice notice-error"><p>';
            echo '<strong>Van As API Plugin Configuratie Fouten:</strong><br>';
            echo implode('<br>', array_map('esc_html', $config_errors));
            echo '</p></div>';
        });
    }
}
?>