<?php
/**
 * Plugin Name: Van As News Importer
 * Description: <PERSON><PERSON><PERSON><PERSON> art<PERSON><PERSON> As Contador API naar WordPress posts
 * Version: 1.0.0
 * Author: Hummingbirds bv
 * Author URI: https://www.hummingbirds.be
 */

// Voorkom directe toegang
if (!defined('ABSPATH')) {
    exit;
}

// Laad configuratie bestand
require_once plugin_dir_path(__FILE__) . 'config.php';

class VanAsAPIImporter {

    private $api_base_url;
    private $username;
    private $password;
    private $option_name = 'van_as_last_import';

    private $import_progress = array();

    public function __construct() {
        // Initialiseer API credentials vanuit config
        $this->api_base_url = VanAsConfig::API_BASE_URL;
        $this->username = VanAsConfig::API_USERNAME;
        $this->password = VanAsConfig::API_PASSWORD;

        add_action('init', array($this, 'init'));
        add_action('van_as_daily_import', array($this, 'import_articles'));
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
        add_action('wp_ajax_van_as_start_import', array($this, 'ajax_start_import'));
        add_action('wp_ajax_van_as_import_progress', array($this, 'ajax_import_progress'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on our plugin page
        if ($hook != 'settings_page_van-as-api') {
            return;
        }

        // Register and enqueue our script
        wp_register_script(
            'van-as-admin-js',
            plugin_dir_url(__FILE__) . 'assets/js/admin.js',
            array('jquery'),
            '1.0.0',
            true
        );

        // Pass variables to script
        wp_localize_script('van-as-admin-js', 'vanAsAdmin', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('van_as_import_nonce'),
            'importing_text' => __('Importeren...', 'van-as-news-importer'),
            'complete_text' => __('Import voltooid!', 'van-as-news-importer'),
            'error_text' => __('Er is een fout opgetreden.', 'van-as-news-importer')
        ));

        wp_enqueue_script('van-as-admin-js');

        // Add inline styles for progress bar
        wp_add_inline_style('admin-bar', '
            .van-as-progress-container {
                width: 100%;
                background-color: #f1f1f1;
                border-radius: 4px;
                margin: 10px 0;
                display: none;
            }
            .van-as-progress-bar {
                height: 20px;
                background-color: #4CAF50;
                border-radius: 4px;
                width: 0%;
                transition: width 0.3s;
                text-align: center;
                line-height: 20px;
                color: white;
                font-weight: bold;
            }
            .van-as-import-status {
                margin: 10px 0;
                font-weight: bold;
                display: none;
            }
        ');
    }

    public function init() {
        // Schedule daily cron job als het nog niet bestaat
        if (!wp_next_scheduled('van_as_daily_import')) {
            wp_schedule_event(time(), 'daily', 'van_as_daily_import');
        }

        // Register the run_import action for manual imports
        add_action('van_as_run_import', array($this, 'import_articles'));
    }

    public function activate() {
        // Schedule eerste import
        wp_schedule_event(time() + 60, 'daily', 'van_as_daily_import');
    }

    public function deactivate() {
        // Remove scheduled event
        wp_clear_scheduled_hook('van_as_daily_import');
    }

    public function add_admin_menu() {
        add_options_page(
            'Van As Contador API Importer',
            'Van As API',
            'manage_options',
            'van-as-api',
            array($this, 'admin_page')
        );
    }

    public function admin_page() {
        // Handle traditional form submission (non-AJAX)
        if (isset($_POST['manual_import']) && wp_verify_nonce($_POST['_wpnonce'], 'van_as_manual_import') && !wp_doing_ajax()) {
            // Start the import process
            $this->start_import_process();

            // Show a message
            echo '<div class="notice notice-info"><p>';
            echo 'Import gestart. Bekijk de voortgang hieronder.';
            echo '</p></div>';
        }

        $last_import = get_option($this->option_name, 'Nog nooit uitgevoerd');
        $next_scheduled = wp_next_scheduled('van_as_daily_import');
        $next_import = $next_scheduled ? date('d-m-Y H:i', $next_scheduled) : 'Niet gepland';

        ?>
        <div class="wrap">
            <h1>Van As Contador API importer</h1>
            <div class="card">
                <h2>Status</h2>
                <p><strong>Laatste import:</strong> <?php echo esc_html($last_import); ?></p>
                <p><strong>Volgende import:</strong> <?php echo esc_html($next_import); ?></p>
            </div>

            <div class="card">
                <h2>Handmatige import</h2>
                <form method="post" id="van-as-import-form">
                    <?php wp_nonce_field('van_as_manual_import'); ?>

                    <input type="submit" name="manual_import" id="van-as-import-button" class="button-primary" value="Nu Importeren">

                    <div class="van-as-progress-container">
                        <div class="van-as-progress-bar">0%</div>
                    </div>
                    <div class="van-as-import-status"></div>
                </form>
            </div>

            <div class="card">
                <h2>Synchronisatie logboek</h2>
                <div style="background: #f1f1f1; padding: 10px; max-height: 300px; overflow-y: auto;">
                    <?php echo nl2br(esc_html($this->get_recent_logs())); ?>
                </div>
            </div>
        </div>
        <?php
    }

    public function import_articles() {
        $this->log('Starting article import...');

        try {
            // Haal artikelen op van API
            $articles = $this->fetch_articles_from_api();

            if (empty($articles)) {
                $this->log('No articles found to import');
                update_option($this->option_name, date('d-m-Y H:i:s') . ' - Geen artikelen gevonden');

                // Update progress if we're tracking it
                $this->update_import_progress(array(
                    'status_message' => 'Geen artikelen gevonden om te importeren',
                    'complete' => true
                ));

                return true;
            }

            $this->log('Processing ' . count($articles) . ' articles');

            // Update progress with total count
            $this->update_import_progress(array(
                'total_articles' => count($articles),
                'status_message' => 'Verwerken van ' . count($articles) . ' artikelen...'
            ));

            $imported_count = 0;
            $skipped_count = 0;
            $error_count = 0;
            $processed_articles = 0;

            foreach ($articles as $article) {
                // Update progress for current article
                $processed_articles++;
                $this->update_import_progress(array(
                    'processed_articles' => $processed_articles,
                    'current_article' => isset($article['title']) ? $article['title'] : 'Onbekend artikel',
                    'status_message' => 'Verwerken van artikel ' . $processed_articles . ' van ' . count($articles)
                ));

                // Controleer of artikel een ID en titel heeft
                if (!isset($article['id']) || empty($article['id'])) {
                    $this->log('Skipping article without ID: ' . json_encode(array_slice($article, 0, 3)));
                    $skipped_count++;
                    $this->update_import_progress(array('skipped_count' => $skipped_count));
                    continue;
                }

                if (!isset($article['title']) || empty($article['title'])) {
                    $this->log('Skipping article without title (ID: ' . $article['id'] . ')');
                    $skipped_count++;
                    $this->update_import_progress(array('skipped_count' => $skipped_count));
                    continue;
                }

                if ($this->article_exists($article)) {
                    $skipped_count++;
                    $this->update_import_progress(array('skipped_count' => $skipped_count));
                    $this->log('Skipping existing article: ' . $article['title'] . ' (ID: ' . $article['id'] . ')');
                    continue;
                }

                if ($this->create_wordpress_post($article)) {
                    $imported_count++;
                    $this->update_import_progress(array('imported_count' => $imported_count));
                    $this->log('Successfully imported article: ' . $article['title'] . ' (ID: ' . $article['id'] . ')');
                } else {
                    $error_count++;
                    $this->update_import_progress(array('error_count' => $error_count));
                    $this->log('Failed to create post for article: ' . $article['title'] . ' (ID: ' . $article['id'] . ')');
                }
            }

            $this->log("Import completed. Imported: {$imported_count}, Skipped: {$skipped_count}, Errors: {$error_count}");
            update_option($this->option_name, current_time('d-m-Y H:i:s'));

            // Mark import as complete
            $this->update_import_progress(array(
                'status_message' => "Import voltooid. Geïmporteerd: {$imported_count}, Overgeslagen: {$skipped_count}, Fouten: {$error_count}",
                'complete' => true
            ));

            return true;

        } catch (Exception $e) {
            $this->log('Import failed: ' . $e->getMessage());

            // Update progress with error
            $this->update_import_progress(array(
                'status_message' => 'Import mislukt: ' . $e->getMessage(),
                'complete' => true,
                'error' => true
            ));

            return false;
        }
    }

    private function fetch_articles_from_api() {
        // Voor de eerste import, gebruik FULL type om alle artikelen op te halen
        $first_import = !get_option($this->option_name . '_timestamp', false);

        $options = array(
            'language' => VanAsConfig::DEFAULT_LANGUAGE
        );

        if ($first_import) {
            // Eerste keer: haal alle artikelen op
            $request_body = VanAsConfig::build_api_request('FULL', $options);

            $this->log("Fetching all articles (first import) - selected=false");
        } else {
            // Voor volgende imports, gebruik LIMIT om de laatste 50 artikelen op te halen
            // Dit is betrouwbaarder dan RANGE met datums
            $options['limit'] = 50; // Haal de laatste 50 artikelen op
            $request_body = VanAsConfig::build_api_request('LIMIT', $options);

            $this->log("Fetching latest 50 articles - selected=false");
        }

        try {
            $articles = $this->make_api_request($request_body);

            // Log aantal gevonden artikelen
            $count = is_array($articles) ? count($articles) : 0;
            $this->log("API returned {$count} articles");

            // Update laatste check timestamp
            update_option($this->option_name . '_timestamp', time());

            return $articles;
        } catch (Exception $e) {
            // Als er een fout optreedt bij het ophalen van artikelen,
            // log de fout en informeer de gebruiker
            $this->log("Error fetching articles: " . $e->getMessage());

            // Waarschuw de gebruiker
            if (!defined('DOING_CRON') || !DOING_CRON) {
                echo '<div class="notice notice-warning"><p>⚠️ Er is een fout opgetreden bij het ophalen van artikelen: ' . esc_html($e->getMessage()) . '</p></div>';
            }

            // Gooi de fout door
            throw $e;
        }
    }

    private function make_api_request($request_body) {
        $url = $this->api_base_url . '/search/item';

        $args = array(
            'method' => 'POST',
            'headers' => array(
                'Authorization' => 'Basic ' . base64_encode($this->username . ':' . $this->password),
                'Content-Type' => 'application/json',
            ),
            'body' => json_encode($request_body),
            'timeout' => VanAsConfig::API_TIMEOUT,
        );

        $this->log('Making API request to: ' . $url);
        $this->log('Request body: ' . json_encode($request_body));

        $response = wp_remote_post($url, $args);

        if (is_wp_error($response)) {
            $error_message = $response->get_error_message();
            $this->log('API request failed with WP error: ' . $error_message);
            throw new Exception('API request failed: ' . $error_message);
        }

        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);

        $this->log('API response status: ' . $status_code);

        if ($status_code === 400) {
            $this->log('API returned 400 Bad Request: ' . $body);
            $this->log('Request was: ' . json_encode($request_body));
            throw new Exception('API Bad Request (400). Check request format. Response: ' . $body);
        }

        if ($status_code === 500) {
            $this->log('API returned 500 error: ' . $body);
            throw new Exception('API server error (500). Check API credentials and request format.');
        }

        if ($status_code !== 200) {
            $this->log('API returned non-200 status: ' . $status_code . ' - ' . $body);
            throw new Exception('API returned status code: ' . $status_code);
        }

        $data = json_decode($body, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $this->log('Invalid JSON response: ' . json_last_error_msg());
            $this->log('Response body: ' . substr($body, 0, 500) . '...');
            throw new Exception('Invalid JSON response from API: ' . json_last_error_msg());
        }

        // Check of response een array is (meerdere artikelen) of een object (enkel artikel)
        if (isset($data['id'])) {
            // Enkel artikel - zet in array
            $this->log('Response contains a single article, converting to array');
            $data = array($data);
        } elseif (!is_array($data)) {
            $this->log('Unexpected response format: ' . gettype($data));
            $this->log('Response preview: ' . substr(json_encode($data), 0, 200));
            throw new Exception('Unexpected API response format');
        }

        $count = count($data);
        $this->log('Successfully fetched ' . $count . ' articles from API');

        return $data;
    }

    private function article_exists($article) {
        if (!isset($article['id']) || empty($article['id'])) {
            $this->log('Article has no ID, cannot check if it exists');
            return true; // Skip articles without ID
        }

        // Check of artikel al bestaat gebaseerd op externe ID
        $existing_posts = get_posts(array(
            'post_type' => VanAsConfig::POST_TYPE,
            'meta_query' => array(
                array(
                    'key' => 'van_as_article_id',
                    'value' => $article['id'],
                    'compare' => '='
                )
            ),
            'posts_per_page' => 1,
            'post_status' => array('publish', 'draft', 'private')
        ));

        if (!empty($existing_posts)) {
            $this->log('Article with ID ' . $article['id'] . ' already exists as post ID ' . $existing_posts[0]->ID);
            return true;
        }

        return false;
    }

    private function create_wordpress_post($article) {
        // Valideer verplichte velden
        if (empty($article['title']) || empty($article['body'])) {
            $this->log('Article missing required fields (title or body): ' . json_encode($article));
            return false;
        }

        // Filter content volgens configuratie
        $content = VanAsConfig::filter_content($article['body']);
        if ($content === false) {
            $this->log('Article content filtered out (too short): ' . $article['title']);
            return false;
        }

        // Process inline images in content
        $content = $this->process_inline_images($content);

        // Maak WordPress post aan
        $post_data = array(
            'post_title' => sanitize_text_field($article['title']),
            'post_content' => $content,
            'post_status' => VanAsConfig::POST_STATUS,
            'post_type' => VanAsConfig::POST_TYPE,
            'post_author' => VanAsConfig::DEFAULT_AUTHOR_ID,
        );

        // Voeg excerpt toe als beschikbaar (shortText veld)
        if (!empty($article['shortText'])) {
            $post_data['post_excerpt'] = sanitize_text_field($article['shortText']);
        }

        // Voeg publicatie datum toe als beschikbaar
        if (!empty($article['publishedOn'])) {
            try {
                $published_date = new DateTime($article['publishedOn']);
                $post_data['post_date'] = $published_date->format('Y-m-d H:i:s');
                $post_data['post_date_gmt'] = gmdate('Y-m-d H:i:s', $published_date->getTimestamp());
            } catch (Exception $e) {
                $this->log('Invalid date format for article ' . $article['id'] . ': ' . $article['publishedOn']);
            }
        }

        // Als er geen publicatie datum is, gebruik de artikel datum
        if (empty($post_data['post_date']) && !empty($article['date'])) {
            try {
                $article_date = new DateTime($article['date']);
                $post_data['post_date'] = $article_date->format('Y-m-d H:i:s');
                $post_data['post_date_gmt'] = gmdate('Y-m-d H:i:s', $article_date->getTimestamp());
            } catch (Exception $e) {
                $this->log('Invalid date format for article ' . $article['id'] . ': ' . $article['date']);
            }
        }

        $post_id = wp_insert_post($post_data);

        if (is_wp_error($post_id)) {
            $this->log('Failed to create post: ' . $post_id->get_error_message());
            return false;
        }

        // Voeg meta data toe
        add_post_meta($post_id, 'van_as_article_id', $article['id']);
        add_post_meta($post_id, 'van_as_source', 'contador_api');
        add_post_meta($post_id, 'van_as_original_url', $article['url'] ?? '');
        add_post_meta($post_id, 'van_as_specific_tenant', $article['specificTenant'] ?? '');
        add_post_meta($post_id, 'van_as_tone_of_voice', $article['toneOfVoice'] ?? false);

        if (!empty($article['origArticleId'])) {
            add_post_meta($post_id, 'van_as_orig_article_id', $article['origArticleId']);
        }

        // Voeg categorieën toe als beschikbaar
        if (!empty($article['categories']) && is_array($article['categories'])) {
            $category_ids = array();
            foreach ($article['categories'] as $category) {
                if (isset($category['name'])) {
                    $category_name = sanitize_text_field($category['name']);
                    $wp_category = get_category_by_slug(sanitize_title($category_name));

                    if (!$wp_category) {
                        $category_id = wp_create_category($category_name);
                        if (!is_wp_error($category_id)) {
                            $category_ids[] = $category_id;
                        }
                    } else {
                        $category_ids[] = $wp_category->term_id;
                    }
                }
            }

            if (!empty($category_ids)) {
                wp_set_post_categories($post_id, $category_ids);
            }
        }

        $this->log('Created post: ' . $article['title'] . ' (ID: ' . $post_id . ', Original ID: ' . $article['id'] . ')');
        return true;
    }

    private function log($message) {
        if (!VanAsConfig::ENABLE_LOGGING) {
            return;
        }

        $log_entry = date('Y-m-d H:i:s') . ' - ' . $message;
        $log_file = VanAsConfig::LOG_FILE_PATH;

        // Behoud alleen laatste MAX_LOG_LINES regels
        $existing_logs = file_exists($log_file) ? file($log_file) : array();
        if (count($existing_logs) > VanAsConfig::MAX_LOG_LINES) {
            $existing_logs = array_slice($existing_logs, -(VanAsConfig::MAX_LOG_LINES - 100));
        }

        $existing_logs[] = $log_entry . "\n";
        file_put_contents($log_file, implode('', $existing_logs));

        // Log ook naar WordPress debug log als WP_DEBUG aan staat
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log('Van As API: ' . $message);
        }
    }

    private function get_recent_logs() {
        $log_file = VanAsConfig::LOG_FILE_PATH;
        if (!file_exists($log_file)) {
            return 'Nog geen logs beschikbaar.';
        }

        $logs = file($log_file);
        $recent_logs = array_slice($logs, -50); // Laatste 50 regels
        return implode('', array_reverse($recent_logs));
    }

    /**
     * Start the import process and initialize progress tracking
     *
     * @return bool
     */
    public function start_import_process() {
        // Initialize progress tracking
        $this->import_progress = array(
            'start_time' => time(),
            'total_articles' => 0,
            'processed_articles' => 0,
            'imported_count' => 0,
            'skipped_count' => 0,
            'error_count' => 0,
            'current_article' => '',
            'status_message' => 'Importeren gestart...',
            'percentage' => 0,
            'complete' => false
        );

        // Save initial progress
        update_option('van_as_import_progress', $this->import_progress);

        // Start the import in a non-blocking way
        wp_schedule_single_event(time(), 'van_as_run_import');
        spawn_cron();

        return true;
    }

    /**
     * AJAX handler for starting the import
     */
    public function ajax_start_import() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'van_as_import_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        // Start the import process
        $result = $this->start_import_process();

        if ($result) {
            wp_send_json_success(array('message' => 'Import gestart'));
        } else {
            wp_send_json_error(array('message' => 'Kon import niet starten'));
        }
    }

    /**
     * AJAX handler for checking import progress
     */
    public function ajax_import_progress() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'van_as_import_nonce')) {
            wp_send_json_error(array('message' => 'Security check failed'));
        }

        // Get current progress
        $progress = get_option('van_as_import_progress', array());

        if (empty($progress)) {
            wp_send_json_error(array('message' => 'Geen import actief'));
        }

        wp_send_json_success($progress);
    }

    /**
     * Update the import progress
     */
    private function update_import_progress($data) {
        // Get current progress
        $progress = get_option('van_as_import_progress', array());

        // Update with new data
        $progress = array_merge($progress, $data);

        // Calculate percentage if we have total articles
        if (!empty($progress['total_articles']) && $progress['total_articles'] > 0) {
            $progress['percentage'] = round(($progress['processed_articles'] / $progress['total_articles']) * 100);
        }

        // Save updated progress
        update_option('van_as_import_progress', $progress);

        // Update instance variable
        $this->import_progress = $progress;
    }

    /**
     * Process inline images in content
     *
     * @param string $content The post content
     * @return string The processed content with updated image URLs
     */
    private function process_inline_images($content) {
        // Check if content contains any images
        if (strpos($content, '<img') === false) {
            return $content;
        }

        $this->log('Processing inline images in content');

        // Create a DOM document to parse the HTML
        $dom = new DOMDocument();

        // Suppress errors from malformed HTML
        libxml_use_internal_errors(true);

        // Add a base HTML structure to ensure proper parsing
        $html = '<!DOCTYPE html><html><head><meta charset="UTF-8"></head><body>' . $content . '</body></html>';

        // Load the content
        $dom->loadHTML($html);

        // Reset errors
        libxml_clear_errors();

        // Find all img tags
        $images = $dom->getElementsByTagName('img');

        // If no images found, return original content
        if ($images->length === 0) {
            $this->log('No images found in content');
            return $content;
        }

        $this->log('Found ' . $images->length . ' inline images');

        // Process each image
        $images_to_process = array();
        foreach ($images as $img) {
            $src = $img->getAttribute('src');

            // Check for both relative and absolute URLs from the API server
            $is_api_image = false;

            // Check for relative paths starting with /media/
            if (strpos($src, '/media/') === 0) {
                $is_api_image = true;
            }

            // Check for absolute URLs from the API domain
            if (strpos($src, 'vanas.contador.be/media/') !== false) {
                $is_api_image = true;
            }

            if ($is_api_image) {
                $this->log('Found API image: ' . $src);
                $images_to_process[] = array(
                    'element' => $img,
                    'src' => $src,
                    'data_image' => $img->getAttribute('data-image')
                );
            }
        }

        // If no API images found, return original content
        if (empty($images_to_process)) {
            return $content;
        }

        // Process each API image
        foreach ($images_to_process as $img_data) {
            $img = $img_data['element'];
            $src = $img_data['src'];
            $data_image = $img_data['data_image'];

            // Get image filename from path
            $filename = basename($src);

            // Download and add to media library
            $attachment_id = $this->import_image_to_media_library($src, $filename, $data_image);

            if ($attachment_id) {
                // Get the new image URL
                $new_image_url = wp_get_attachment_url($attachment_id);

                // Update the src attribute
                $img->setAttribute('src', $new_image_url);

                // Add WordPress attachment class
                $class = $img->getAttribute('class');
                $img->setAttribute('class', trim($class . ' wp-image-' . $attachment_id));

                $this->log('Updated inline image: ' . $filename . ' -> ' . $new_image_url);
            }
        }

        // Get the body element
        $body = $dom->getElementsByTagName('body')->item(0);

        // If we have a body element, get its inner HTML
        if ($body) {
            // Create a new DOMDocument to hold just the body content
            $body_dom = new DOMDocument();

            // Import each child node of the body
            foreach ($body->childNodes as $child) {
                $imported_node = $body_dom->importNode($child, true);
                $body_dom->appendChild($imported_node);
            }

            // Get the HTML content
            $updated_content = $body_dom->saveHTML();
            $this->log('Successfully extracted updated content with images');
        } else {
            // Fallback to the original method if body not found
            $updated_content = $dom->saveHTML($dom->documentElement);

            // Extract only the body content
            $body_start = strpos($updated_content, '<body>');
            $body_end = strpos($updated_content, '</body>');

            if ($body_start !== false && $body_end !== false) {
                $updated_content = substr($updated_content, $body_start + 6, $body_end - $body_start - 6);
            }

            // Clean up the content
            $updated_content = preg_replace('/<\/?html>|<\/?body>|<!DOCTYPE.*?>/i', '', $updated_content);
        }

        return $updated_content;
    }

    /**
     * Import an image from the API to the WordPress media library
     *
     * @param string $image_url The image URL from the API
     * @param string $filename The image filename
     * @param string $data_image The data-image attribute value (for reference)
     * @return int|false The attachment ID if successful, false otherwise
     */
    private function import_image_to_media_library($image_url, $filename, $data_image = '') {
        // Check if image already exists in media library by data-image attribute
        if (!empty($data_image)) {
            $existing_attachment = get_posts(array(
                'post_type' => 'attachment',
                'meta_key' => 'van_as_data_image',
                'meta_value' => $data_image,
                'posts_per_page' => 1
            ));

            if (!empty($existing_attachment)) {
                $this->log('Image already exists in media library: ' . $data_image);
                return $existing_attachment[0]->ID;
            }
        }

        // Determine the full URL to the image on the API server
        $full_url = $image_url;

        // If it's a relative URL, prepend the base URL
        if (strpos($image_url, 'http') !== 0) {
            // Remove any leading slash for consistency
            $image_url = ltrim($image_url, '/');

            // If the URL starts with 'media/', prepend the base domain
            if (strpos($image_url, 'media/') === 0) {
                $full_url = 'https://vanas.contador.be/' . $image_url;
            } else {
                // Otherwise use the configured API base URL
                $full_url = rtrim(VanAsConfig::API_BASE_URL, '/') . '/' . $image_url;
            }
        }

        $this->log('Downloading image from: ' . $full_url);

        // Make the request to download the image
        $response = wp_remote_get($full_url, array(
            'timeout' => VanAsConfig::API_TIMEOUT,
            'headers' => array(
                'Authorization' => 'Basic ' . base64_encode($this->username . ':' . $this->password)
            )
        ));

        // Check for errors
        if (is_wp_error($response)) {
            $this->log('Failed to download image: ' . $response->get_error_message());
            return false;
        }

        // Check response code
        $status_code = wp_remote_retrieve_response_code($response);
        if ($status_code !== 200) {
            $this->log('Failed to download image, status code: ' . $status_code);
            return false;
        }

        // Get the image data
        $image_data = wp_remote_retrieve_body($response);

        // Check if we got any data
        if (empty($image_data)) {
            $this->log('Empty image data received for: ' . $image_url);
            return false;
        }

        // Get WordPress upload directory
        $upload_dir = wp_upload_dir();

        // Generate unique filename
        $unique_filename = wp_unique_filename($upload_dir['path'], $filename);

        // Full path to the file
        $file_path = $upload_dir['path'] . '/' . $unique_filename;

        // Save the image file
        if (!file_put_contents($file_path, $image_data)) {
            $this->log('Failed to save image to: ' . $file_path);
            return false;
        }

        // Check the file type
        $file_type = wp_check_filetype($unique_filename, null);
        if (empty($file_type['type'])) {
            $this->log('Unknown file type for image: ' . $unique_filename);
            unlink($file_path); // Delete the file
            return false;
        }

        // Prepare attachment data
        $attachment = array(
            'post_mime_type' => $file_type['type'],
            'post_title' => sanitize_file_name(pathinfo($unique_filename, PATHINFO_FILENAME)),
            'post_content' => '',
            'post_status' => 'inherit'
        );

        // Insert attachment into the database
        $attachment_id = wp_insert_attachment($attachment, $file_path);

        if (!$attachment_id || is_wp_error($attachment_id)) {
            $this->log('Failed to insert attachment: ' . ($attachment_id->get_error_message() ?? 'Unknown error'));
            unlink($file_path); // Delete the file
            return false;
        }

        // Include image processing functions
        require_once(ABSPATH . 'wp-admin/includes/image.php');

        // Generate metadata and thumbnails
        $attachment_data = wp_generate_attachment_metadata($attachment_id, $file_path);
        wp_update_attachment_metadata($attachment_id, $attachment_data);

        // Store the data-image attribute as meta for future reference
        if (!empty($data_image)) {
            add_post_meta($attachment_id, 'van_as_data_image', $data_image);
        }

        $this->log('Successfully imported image to media library: ' . $unique_filename . ' (ID: ' . $attachment_id . ')');

        return $attachment_id;
    }
}

// Initialiseer de plugin
new VanAsAPIImporter();


?>